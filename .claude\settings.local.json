{"permissions": {"allow": ["WebFetch(domain:github.com)", "Bash(git clone https://github.com/mrcxlinux/exynos9810-kernel-artplus)", "<PERSON><PERSON>(git pull)", "Bash(rm -rf exynos9810-kernel-artplus)", "Bash(git restore --source=HEAD :/)", "<PERSON><PERSON>(dir)", "Bash(wsl -e bash -c \"cd ~ && git clone https://github.com/mrcxlinux/exynos9810-kernel-artplus\")", "Bash(wsl --list --verbose)", "<PERSON>sh(wsl -d Ubuntu -e bash -c \"echo ''WSL is working correctly''\")", "Bash(wsl --update)", "Bash(dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart)", "Bash(systeminfo)", "<PERSON><PERSON>(wsl --shutdown)", "Ba<PERSON>(wsl --distribution Ubuntu)", "<PERSON><PERSON>(wsl --unregister Ubuntu)", "<PERSON><PERSON>(wsl --install -d Ubuntu)", "Bash(dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart)", "Bash(shutdown /r /t 0)", "Bash(wsl --list --online)", "Bash(wsl -d Ubuntu -e bash -c \"git clone https://github.com/mrcxlinux/exynos9810-kernel-artplus\")", "Bash(dir exynos9810-kernel-artplus)", "Bash(wsl -d Ubuntu -e bash -c \"ls -la ~\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && ls -la net/wireguard/\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && make exynos9810_defconfig\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && ls -la net/wireguard/src/\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && rm -rf net/wireguard/* && cd net/wireguard && git clone https://git.zx2c4.com/wireguard-linux-compat . && cd ../..\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && ls -la net/wireguard/src/ | grep Kconfig\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && export ANDROID_MAJOR_VERSION=q && make exynos9810_defconfig\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && export CURL_OPTIONS=''-k'' && export ANDROID_MAJOR_VERSION=q && make exynos9810_defconfig\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && ls -la net/wireguard/src/Kconfig\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && find net/wireguard -name ''Kconfig''\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && ls -la net/wireguard && ls -la net/wireguard/*\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && rm -rf net/wireguard && git clone https://git.zx2c4.com/wireguard-linux-compat net/wireguard\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && head -90 net/Kconfig\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && ls -la net/wireguard/ | grep Kconfig\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && ln -s src/Kconfig net/wireguard/Kconfig\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && file net/wireguard/Kconfig\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && pwd && ls -la net/wireguard/\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && mkdir -p net/wireguard && ln -s ../wireguard/src/Kconfig net/wireguard/Kconfig\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && rm -f net/wireguard/Kconfig && ln -s src/Kconfig net/wireguard/Kconfig\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && ls -la net/wireguard/ && file net/wireguard/Kconfig\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && rm -f net/wireguard/Kconfig && cd net/wireguard && ln -s src/Kconfig Kconfig && cd ../..\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && rm -rf net/wireguard && git clone https://git.zx2c4.com/wireguard-linux-compat net/wireguard && ls -la net/wireguard/\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && ls -la net/wireguard/src/ && ln -s src/Kconfig net/wireguard/Kconfig && ls -la net/wireguard/Kconfig\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && export ANDROID_MAJOR_VERSION=q && export CURL_OPTIONS=''-k'' && make exynos9810_defconfig\")", "WebSearch", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && export CURL_INSECURE=1 && export GIT_SSL_NO_VERIFY=1\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && rm -f net/wireguard/Kconfig && cd net/wireguard && ln -s src/Kconfig Kconfig && cd ../.. && ls -la net/wireguard/Kconfig\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && export ANDROID_MAJOR_VERSION=q && export CURL_INSECURE=1 && export GIT_SSL_NO_VERIFY=1 && make exynos9810_defconfig\")", "Bash(wsl -d Ubuntu -e bash -c \"cd /mnt/c/Sviluppo/kernelsu-galaxys9/wsl/exynos9810-kernel-artplus && ln -sf src/Kconfig net/wireguard/Kconfig && ls -la net/wireguard/Kconfig\")"], "deny": [], "ask": []}}