// -*- C++ -*-
//===-----------------------------------------------------------------------===//
//
// Part of the LLVM Project, under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//

#ifndef _LIBCPP___LOCALE_LOCALE_BASE_API_FUCHSIA_H
#define _LIBCPP___LOCALE_LOCALE_BASE_API_FUCHSIA_H

#if defined(__Fuchsia__)

#  include <__support/xlocale/__posix_l_fallback.h>
#  include <__support/xlocale/__strtonum_fallback.h>
#  include <cstdlib>
#  include <cwchar>

#endif // defined(__Fuchsia__)

#endif // _LIBCPP___LOCALE_LOCALE_BASE_API_FUCHSIA_H
