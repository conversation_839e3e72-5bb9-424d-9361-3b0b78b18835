// -*- C++ -*-
//===----------------------------------------------------------------------===//
//
// Part of the LLVM Project, under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//

#ifndef _LIBCPP_EXPERIMENTAL_CONFIG
#define _LIBCPP_EXPERIMENTAL_CONFIG

#include <__config>

#if !defined(_LIBCPP_HAS_NO_PRAGMA_SYSTEM_HEADER)
#  pragma GCC system_header
#endif

#define _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL                                                                           \
  namespace std {                                                                                                      \
  namespace experimental {
#define _LIBCPP_END_NAMESPACE_EXPERIMENTAL                                                                             \
  }                                                                                                                    \
  }

#define _LIBCPP_BEGIN_NAMESPACE_LFTS _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL inline namespace fundamentals_v1 {
#define _LIBCPP_END_NAMESPACE_LFTS                                                                                     \
  }                                                                                                                    \
  }                                                                                                                    \
  }

#define _LIBCPP_BEGIN_NAMESPACE_LFTS_V2 _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL inline namespace fundamentals_v2 {
#define _LIBCPP_END_NAMESPACE_LFTS_V2                                                                                  \
  }                                                                                                                    \
  }                                                                                                                    \
  }

// TODO: support more targets
#if defined(__AVX__)
#  define _LIBCPP_NATIVE_SIMD_WIDTH_IN_BYTES 32
#else
#  define _LIBCPP_NATIVE_SIMD_WIDTH_IN_BYTES 16
#endif

#endif
